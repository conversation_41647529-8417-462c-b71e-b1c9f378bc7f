#!/bin/bash

# UNFEEABLE Rules Test Runner
# This script runs the comprehensive UNFEEABLE test suite

set -e

echo "🧪 Running UNFEEABLE Rules Test Suite"
echo "======================================"

# Change to solver directory
cd solver

echo "📋 Test Categories:"
echo "  1. Fee Capping Tests"
echo "  2. Legality Query Tests" 
echo "  3. Edge Case Tests"
echo "  4. Maximum Fee Constraint Tests"
echo ""

# Run all UNFEEABLE tests
echo "🚀 Running all UNFEEABLE tests..."
cargo test unfeeable_tests --lib -- --nocapture

echo ""
echo "✅ UNFEEABLE test suite completed!"
echo ""
echo "📊 Test Summary by Category:"
echo ""

# Run tests by category with counts
echo "🔧 Fee Capping Tests:"
cargo test unfeeable_tests --lib -- --list | grep -E "(fee_cap|boundary)" | wc -l | xargs echo "   Tests:"

echo ""
echo "⚖️  Legality Query Tests:"
cargo test unfeeable_tests --lib -- --list | grep -E "(legality|illegality)" | wc -l | xargs echo "   Tests:"

echo ""
echo "🎯 Edge Case Tests:"
cargo test unfeeable_tests --lib -- --list | grep -E "(boundary|missing|extreme|stress)" | wc -l | xargs echo "   Tests:"

echo ""
echo "📈 Maximum Fee Constraint Tests:"
cargo test unfeeable_tests --lib -- --list | grep -E "(california|florida|texas|no_unfeeable)" | wc -l | xargs echo "   Tests:"

echo ""
echo "📖 For detailed documentation, see: solver/tests/UNFEEABLE_TEST_DOCUMENTATION.md"
