#!/bin/bash

# UNFEEABLE Rules Test Runner
# This script runs the comprehensive UNFEEABLE test suite

set -e

echo "🧪 Running UNFEEABLE Rules Test Suite"
echo "======================================"

# Change to solver directory
cd solver

echo "📋 Test Categories:"
echo "  1. Fee Capping Tests"
echo "  2. Legality Query Tests" 
echo "  3. Edge Case Tests"
echo "  4. Maximum Fee Constraint Tests"
echo ""

# Run all UNFEEABLE tests
echo "🚀 Running UNFEEABLE tests sequentially to avoid server conflicts..."

echo ""
echo "🔧 Testing Fee Capping Rules:"
echo "   Arizona (fee cap 500)..."
(cd solver && cargo test test_arizona_fee_cap_500 --test unfeeable_tests -- --nocapture --quiet)

echo "   Arkansas (fee cap 0 with amountToCustomer > 10k)..."
(cd solver && cargo test test_arkansas_fee_cap_0_with_amount_to_customer --test unfeeable_tests -- --nocapture --quiet)

echo "   Colorado (fee cap 100 with amountToCustomer > 30k)..."
(cd solver && cargo test test_colorado_fee_cap_100_with_amount_to_customer --test unfeeable_tests -- --nocapture --quiet)

echo "   New Jersey (fee cap 0 always)..."
(cd solver && cargo test test_new_jersey_fee_cap_0_always --test unfeeable_tests -- --nocapture --quiet)

echo ""
echo "⚖️  Testing Legality Queries:"
echo "   Arkansas legality with zero fee..."
(cd solver && cargo test test_arkansas_legality_with_zero_fee_high_amount_to_customer --test unfeeable_tests -- --nocapture --quiet)

echo "   Colorado legality with fee at cap..."
(cd solver && cargo test test_colorado_legality_with_fee_100_high_amount_to_customer --test unfeeable_tests -- --nocapture --quiet)

echo ""
echo "🎯 Testing Edge Cases:"
echo "   Arkansas boundary conditions..."
(cd solver && cargo test test_arkansas_boundary_amount_to_customer_exactly_10k --test unfeeable_tests -- --nocapture --quiet)

echo "   Colorado boundary conditions..."
(cd solver && cargo test test_colorado_boundary_amount_to_customer_exactly_30k --test unfeeable_tests -- --nocapture --quiet)

echo ""
echo "📈 Testing Maximum Fee Constraints (No UNFEEABLE Rules):"
echo "   California (no UNFEEABLE rules)..."
(cd solver && cargo test test_california_no_unfeeable_rule_max_fee --test unfeeable_tests -- --nocapture --quiet)

echo "   Texas (no UNFEEABLE rules)..."
(cd solver && cargo test test_texas_no_unfeeable_rule_max_fee --test unfeeable_tests -- --nocapture --quiet)

echo ""
echo "✅ UNFEEABLE test suite completed!"
echo ""
echo "📊 Test Summary by Category:"
echo ""

echo ""
echo "📊 Test Summary:"
echo "   ✅ Fee Capping Tests: 4 representative tests passed"
echo "   ✅ Legality Query Tests: 2 representative tests passed"
echo "   ✅ Edge Case Tests: 2 boundary condition tests passed"
echo "   ✅ Maximum Fee Constraint Tests: 2 no-UNFEEABLE tests passed"
echo ""
echo "   Total Representative Tests: 10 passed"
echo "   Full Test Suite: 54 tests available (run individually to avoid server conflicts)"

echo ""
echo "📖 For detailed documentation, see: solver/tests/UNFEEABLE_TEST_DOCUMENTATION.md"
