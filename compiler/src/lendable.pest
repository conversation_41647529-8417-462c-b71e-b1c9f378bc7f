//variables
var_amount = @{ "amount" }
var_apr = @{ "apr" }
var_term = @{ "term" }
var_fee = @{ "fee" }
var_amount_to_customer = @{ "amountToCustomer" }

number = @{ ('0'..'9')+ ~ ("." ~ ('0'..'9')+)? }
amount_unit = { "k" | "m" }
time_unit = { "mo" | "yr" }
fee_unit = { "k" | "m" }
amount = ${ number ~ amount_unit }
time = ${ number ~ time_unit }
percentage = ${ number ~ "%" }
fee = ${ number ~ fee_unit | number }
amountvar = _{ var_amount }
amountval = _{ amount | amountvar }
percentagevar = _{ var_apr }
percentageval = _{ percentage | percentagevar }
timevar = _{ var_term }
timeval = _{ time | timevar }
feevar = _{ var_fee }
feeval = _{ fee | feevar }
amounttocustomervar = _{ var_amount_to_customer }
amounttocustomerval = _{ amount | amounttocustomervar }
lte = { "<=" }
gte = { ">=" }
lt = { "<"  }
gt = { ">"  }
eq = { "="  }
neq = { "!=" }
amount_range = { "AMOUNT" ~ whitespace+ ~ "RANGE" ~ whitespace+ ~ amount ~ "-" ~ amount ~ "." }
term_range = { "TERM" ~ whitespace+ ~  "RANGE" ~ whitespace+ ~ time ~ "-" ~ time ~ "." }
jur = { 'a'..'z'{2} }
and_op = { "AND" }
or_op = { "OR" }
bool_op = { and_op | or_op }
comp_op = { lte | gte | lt | gt | eq | neq }
percentage_comp_expr = { percentagevar ~ whitespace+ ~ comp_op ~ whitespace+ ~ percentageval } // apr > 21%
amount_comp_expr = { amountvar ~ whitespace+ ~ comp_op ~ whitespace+ ~ amountval }
fee_comp_expr = { feevar ~ whitespace+ ~ comp_op ~ whitespace+ ~ feeval }
time_comp_expr = { timevar ~ whitespace+ ~ comp_op ~ whitespace+ ~ timeval }
amount_to_customer_comp_expr = { amounttocustomervar ~ whitespace+ ~ comp_op ~ whitespace+ ~ amounttocustomerval }
country_expr = { "COUNTRY" ~ whitespace+ ~ jur }
province_expr = { "PROVINCE" ~ whitespace+ ~ jur }
is_personal = { ("IS" ~ whitespace+)? ~ "PERSONAL"}
is_business = { ("IS" ~ whitespace+)? ~ "BUSINESS"}
interest = { "io" | "pni" }
interest_expr = { "INTEREST" ~ whitespace+ ~ interest }
jur_expr = { country_expr | province_expr }
not_expr = { "NOT" ~ whitespace+ ~ bool }
true_val = { "TRUE" }
false_val = { "FALSE" }
bool = { percentage_comp_expr | amount_comp_expr | fee_comp_expr | time_comp_expr | amount_to_customer_comp_expr | jur_expr | interest_expr | not_expr | is_personal | is_business | true_val | false_val | ("(" ~ expr ~ ")") }
expr = { bool ~ (whitespace+ ~ bool_op ~ whitespace+ ~ bool)* }
for_stmt = { "FOR" ~ whitespace+ ~ expr ~ whitespace* ~ ":" ~ newline }
illegalif_stmt = { "ILLEGAL" ~ whitespace+ ~ "IF" ~ whitespace+ ~ expr ~ "." ~ newline }
legalif_stmt = { "LEGAL" ~ whitespace+ ~ "IF" ~ whitespace+ ~ expr ~ "." ~ newline }
unfeeable_stmt = { "UNFEEABLE" ~ (whitespace+ ~ "WHEN" ~ whitespace+ ~ expr)* ~ "." ~ newline }
feeable_stmt = { "FEEABLE" ~ (whitespace+ ~ "WHEN" ~ whitespace+ ~ expr)* ~ "." ~ newline }
lendable_stmt = { "LENDABLE" ~ "." ~ newline }

tab = @{ "\t" | "  " }
newline = ${ whitespace* ~ "\n" ~ tab* }
whitespace = _{ " " }

line = _{ (for_stmt | lendable_stmt | illegalif_stmt | legalif_stmt | unfeeable_stmt | feeable_stmt) ~ newline*}
lines = _{ (line)+ }
tok_list = _{ newline* ~ amount_range ~ newline* ~ term_range ~ newline* ~ lines ~ EOI }