version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - mv .git ..
      - $(aws ecr get-login --region $AWS_DEFAULT_REGION --no-include-email)
      - REPOSITORY_URI=772817959278.dkr.ecr.${AWS_DEFAULT_REGION}.amazonaws.com/${Service}
      - DATE=$(date +%Y%m%d%H%M%S)
      - GIT_COMMIT=$(cat gitCommit.out | cut -c 1-7)
      - GIT_TAG=${GIT_COMMIT}
      # The following environment variables need to be passed to the build
      - echo Service ${Service}
      - echo Docker run image ${DockerFile}
      - echo Git tag $GIT_TAG
      - echo Desired tag ${ImageTag}
      - aws s3 cp s3://salt-devops/deploy-scripts/transform_template_master.py ./deploy-scripts/transform_template.py
      - aws s3 cp s3://salt-devops/deploy-scripts/copy_param_to_file.py ./deploy-scripts
      - cp ./DevOps/Dockers/Dockerfile .
      - cp ./DevOps/Dockers/.dockerignore .
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build -t $REPOSITORY_URI:${ImageTag} .
      - if [[ $ImageTag == dev ]] ; then docker tag $REPOSITORY_URI:${ImageTag} $REPOSITORY_URI:eph-$GIT_TAG ; else docker tag $REPOSITORY_URI:${ImageTag} $REPOSITORY_URI:$GIT_TAG ; fi
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker images...
      - docker push $REPOSITORY_URI:${ImageTag}
      - if [[ $ImageTag == dev ]] ; then docker push $REPOSITORY_URI:eph-$GIT_TAG ; else docker push $REPOSITORY_URI:$GIT_TAG ; fi
      - if [[ $ImageTag == dev ]] ; then aws s3 cp s3://salt-devops/Terraform_Repo/ ./TF --recursive ; mv ../.git . ; fi
      - echo Writing image definitions file...
      - printf '[{"name":"%s","imageUri":"%s"}]' ${Service} $REPOSITORY_URI:$IMAGE_TAG > imagedefinitions.json
artifacts:
    files:
      - '**/*'