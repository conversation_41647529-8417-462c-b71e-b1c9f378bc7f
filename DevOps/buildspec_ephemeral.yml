version: 0.2

env:
  parameter-store:
    NPM_TOKEN: '/build/npmToken'
  variables:
    Environment: 'build'
    Service: 'lendable'

phases:

  pre_build:
    commands:
      - BRANCH_NAME=$(echo $CODEBUILD_WEBHOOK_HEAD_REF | cut -c 12-)
  build:
    commands:
      - echo $BRANCH_NAME
      - sed 's/placeholder/'$BRANCH_NAME'/' ./DevOps/lendable_dsl_pipeline.template.json > DevOps/lendable_dsl_pipeline.json
      - echo new template
      - cat DevOps/lendable_dsl_pipeline.json
      - aws codepipeline create-pipeline --cli-input-json file://DevOps/lendable_dsl_pipeline.json

artifacts:
  files:
    - '**/*'
  base-directory: 'build'

