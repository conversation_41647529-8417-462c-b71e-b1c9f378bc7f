{"pipeline": {"name": "lendable-dsl-ephemeral-placeholder", "roleArn": "arn:aws:iam::772817959278:role/AWS-CodePipeline-Service", "artifactStore": {"type": "S3", "location": "codepipeline-us-west-2-823326684567"}, "stages": [{"name": "Source", "actions": [{"name": "Source", "actionTypeId": {"category": "Source", "owner": "AWS", "provider": "CodeStarSourceConnection", "version": "1"}, "runOrder": 1, "configuration": {"BranchName": "placeholder", "ConnectionArn": "arn:aws:codestar-connections:us-west-2:772817959278:connection/beaaa1c0-dcb7-4bf8-abf4-1925f44bbd49", "DetectChanges": "true", "FullRepositoryId": "SALTLending/lendable-dsl", "OutputArtifactFormat": "CODEBUILD_CLONE_REF"}, "outputArtifacts": [{"name": "SourceArtifact"}], "inputArtifacts": [], "region": "us-west-2", "namespace": "SourceVariables"}]}, {"name": "Build", "actions": [{"name": "Build", "actionTypeId": {"category": "Build", "owner": "AWS", "provider": "CodeBuild", "version": "1"}, "runOrder": 1, "configuration": {"ProjectName": "Lendable-DSL-Compile-Dev"}, "outputArtifacts": [{"name": "BuildArtifact"}], "inputArtifacts": [{"name": "SourceArtifact"}], "region": "us-west-2", "namespace": "BuildVariables"}]}, {"name": "BuildImage", "actions": [{"name": "BuildImage", "actionTypeId": {"category": "Build", "owner": "AWS", "provider": "CodeBuild", "version": "1"}, "runOrder": 1, "configuration": {"ProjectName": "Lendable-DSL-BuildImage-Dev"}, "outputArtifacts": [{"name": "myappimage"}], "inputArtifacts": [{"name": "BuildArtifact"}], "region": "us-west-2"}]}, {"name": "TF", "actions": [{"name": "TF", "actionTypeId": {"category": "Build", "owner": "AWS", "provider": "CodeBuild", "version": "1"}, "runOrder": 1, "configuration": {"ProjectName": "Lendable-DSL-Create-Env-Dev"}, "outputArtifacts": [], "inputArtifacts": [{"name": "myappimage"}], "region": "us-west-2"}]}]}}