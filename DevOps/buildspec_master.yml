version: 0.2

env:
  variables:
    Environment: "build"
    Service: "lendable"
phases:
  pre_build:
    commands:
      - echo Installing...
      - if [ ! -z $(echo $CODEBUILD_RESOLVED_SOURCE_VERSION) ] ; then echo $CODEBUILD_RESOLVED_SOURCE_VERSION > gitCommit.out ; fi
      - rustup install nightly || rustup install nightly-2019-01-09
  build:
    commands:
      - echo Building...
      - echo Compiling the rust code
      - sh build.sh
  post_build:
    commands:
      - mkdir dist
      - mkdir dist/config
      # These 2 files allows you to customize your ephemeral.
      - if test -f 'config/'"$SERVICE"'_dev.json'; then cp config/"$SERVICE"_dev.json dist/config/"$SERVICE".json; else echo "no repo config"; fi
      - if test -f "config/dev.tfvars"; then cp config/dev.tfvars dist/config/dev.tfvars ; else echo "no repo tfvars"; fi
      - cp -r artifacts/* dist/
      - cp -r DevOps dist
      - cp -r .git dist
      - cd dist
      - echo "#!/bin/bash" > rust_binary.sh
      - echo "./solver" >> rust_binary.sh
      - echo "dmesg" >> rust_binary.sh
      - chmod 755 rust_binary.sh
      - cp -R ../deploy-scripts .
      - if [ -f ../gitCommit.out ] ; then cp ../gitCommit.out ./gitCommit.out ; fi
artifacts:
  files:
    - '**/*'
  base-directory: 'dist'
cache:
  paths:
    - '/root/.cargo/registry/**/*'
    - 'compiler/target/**/*'
    - 'solver/target/**/*'
