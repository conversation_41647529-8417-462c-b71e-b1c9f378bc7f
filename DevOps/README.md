This directory store the instructions for our CICD.
```
buildspec_ephemeral.yml
```
This creates the ephemeral pipeline.
```
buildspec_master.yml
```
This is for the build stage
```
buildspec_image_master.yml
```
This is for the buildimage stage. It packaged the artifacts and store it into ECR

### Setting Up the Ephemeral Environment
https://saltlending.atlassian.net/wiki/spaces/DEVOPS/pages/1671987265/Ephemeral+Dev+Environment+Pipeline