FROM ubuntu:18.04

ENV DEBIAN_FRONTEND noninteractive
RUN apt-get update && apt-get install -y sudo \
                                         build-essential \
                                         findutils \
                                         g++ \
					 gcc \
                                         git-core \
                                         openssh-client \
					 dnsutils \
					 libtool \
                                         python3-pip \
					 build-essential \
                                         wget \
                    			 lsb-release \
                                         curl \
                                         libpq-dev \
                                         redis-server \
                                         unzip \
                                         default-jre \
                                         ca-certificates && \
                                         curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
                                         unzip awscliv2.zip && \
                                         ./aws/install



#add certs to enable ssl so docker can reach out to https sites to get resources for installation
ADD cert1.crt /usr/local/share/ca-certificates/cert1.crt
ADD cert2.crt /usr/local/share/ca-certificates/cert2.crt
RUN update-ca-certificates

# install parity (2.2.9)
ENV PARITY_VERSION=2.2.9
ENV PARITY_URL=https://releases.parity.io/ethereum/v2.2.9/x86_64-unknown-linux-gnu/parity
ENV PARITY_SHA256=472e33b4d6cd8275d16c2a290a5e28b3998fa03923cae17418a5bc712c33ffcd
RUN cd /tmp && \
    wget -qO parity "$PARITY_URL" && \
    echo "$PARITY_SHA256 parity" | sha256sum -c - && \
    mv ./parity /usr/bin/parity && \
    chmod 755 /usr/bin/parity && \
    rm -rf /tmp/*


# install bitcoin binaries (0.16.3)
ENV BITCOIN_VERSION=0.16.3
ENV BITCOIN_URL=https://bitcoincore.org/bin/bitcoin-core-0.16.3/bitcoin-0.16.3-x86_64-linux-gnu.tar.gz
ENV BITCOIN_SHA256=5d422a9d544742bc0df12427383f9c2517433ce7b58cf672b9a9b17c2ef51e4f
ENV BITCOIN_ASC_URL=https://bitcoincore.org/bin/bitcoin-core-0.16.3/SHA256SUMS.asc
ENV BITCOIN_PGP_KEY=0x90C8019E36C2E964

RUN apt-get update \
    && apt-get install -y gnupg2 \
                          openssl
RUN set -ex \
  && cd /tmp \
  && mkdir /opt/bitcoin \
  && wget -qO bitcoin.tar.gz "$BITCOIN_URL" \
  && echo "$BITCOIN_SHA256 bitcoin.tar.gz" | sha256sum -c - \
  && gpg2 --keyserver keyserver.ubuntu.com --recv-keys "$BITCOIN_PGP_KEY" \
  && wget -qO bitcoin.asc "$BITCOIN_ASC_URL" \
  && gpg2 --verify bitcoin.asc \
  && tar -xzvf bitcoin.tar.gz -C /opt/bitcoin --strip-components=1 --exclude=*-qt \
  && rm -rf /tmp/*

# install dogecoin 1.10.0
ENV DOGECOIN_VERSION=1.10.0
ENV DOGECOIN_URL=https://github.com/dogecoin/dogecoin/releases/download/v1.10.0/dogecoin-1.10.0-linux64.tar.gz

#doge install - good dogey
RUN cd /tmp \
    && mkdir /opt/dogecoin \
    && wget -qO dogecoin.tar.gz "$DOGECOIN_URL" \
    && tar -xzvf dogecoin.tar.gz -C /opt/dogecoin --strip-components=1 --exclude=*-qt \
    && rm -rf /tmp/*

# install litecoin 0.16.3
ENV LITECOIN_VERSION=0.16.3
ENV LITECOIN_URL=https://download.litecoin.org/litecoin-0.16.3/linux/litecoin-0.16.3-x86_64-linux-gnu.tar.gz
ENV LITECOIN_SHA256=686d99d1746528648c2c54a1363d046436fd172beadaceea80bdc93043805994

RUN cd /tmp \
    && mkdir /opt/litecoin \
    && wget -qO litecoin.tar.gz "$LITECOIN_URL" \
    && echo "$LITECOIN_SHA256 litecoin.tar.gz" | sha256sum -c - \
    && tar -xzvf litecoin.tar.gz -C /opt/litecoin --strip-components=1 --exclude=*-qt \
    && rm -rf /tmp/litecoin*

#rust
RUN curl https://sh.rustup.rs -sSf | sh -s -- -y
ENV PATH /root/.cargo/bin:$PATH

# stack - Mostly for testing haskell integration
RUN curl -sSL https://get.haskellstack.org/ | sh

#USER root
RUN apt-get update && apt-get install -y libpq-dev
RUN rustup default beta
#RUN rustup install stable
RUN	rustup default 1.56.1
RUN apt-get update && \
	apt-get install -y libssl-dev \ 
        pkg-config

RUN cargo install sqlx-cli

# elasticmq for sqs server
ARG ELASTICMQ_VERSION
ENV ELASTICMQ_VERSION ${ELASTICMQ_VERSION:-1.3.2}
RUN mkdir -p /opt/elasticmq/log /opt/elasticmq/lib /opt/elasticmq/config && \
     curl -sfLo /opt/elasticmq/lib/elasticmq.jar https://s3-eu-west-1.amazonaws.com/softwaremill-public/elasticmq-server-${ELASTICMQ_VERSION}.jar

# postgres
RUN echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list && \
    wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | \
    sudo apt-key add -

RUN apt-get update && \
    apt-get install -y postgresql-10

RUN mkdir /etc/ssl/private-copy; mv /etc/ssl/private/* /etc/ssl/private-copy/; rm -r /etc/ssl/private; mv /etc/ssl/private-copy /etc/ssl/private; chmod -R 0700 /etc/ssl/private; chown -R postgres /etc/ssl/private

USER postgres

RUN usr/lib/postgresql/10/bin/initdb -D /var/lib/postgresql/10/data \
  && sed -i 's/host    all             all             127.0.0.1\/32            ident/host all all 127.0.0.1\/32 md5/g' /var/lib/postgresql/10/data/pg_hba.conf && \
  echo "listen_addresses = '*'" >> /var/lib/postgresql/10/data/postgresql.conf && \
  echo "host    all             all             0.0.0.0/0            trust" >> /var/lib/postgresql/10/data/pg_hba.conf

RUN /usr/lib/postgresql/10/bin/pg_ctl start -w -D '/var/lib/postgresql/10/data' && \
	 sh -c 'createuser root & createdb pgdb' && \
  	 psql -c "ALTER USER root WITH PASSWORD 'password';" && \
  	 psql -c "GRANT ALL PRIVILEGES ON DATABASE pgdb TO root;" && \
  	 psql -d pgdb -c 'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";' && \
         sh -c 'createdb tokensale' && \
         psql -c "ALTER USER root WITH PASSWORD 'password';" && \
  	 psql -c "GRANT ALL PRIVILEGES ON DATABASE pgdb TO root;" && \
         psql -c "GRANT ALL PRIVILEGES ON DATABASE tokensale TO root;" && \
         psql -d pgdb -c 'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";' && \
         psql -d tokensale -c 'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";'

VOLUME /var/lib/docker
CMD ["/usr/lib/postgresql/10/bin/postgres", "-p", "5432", "-D", "/var/lib/postgresql/10/data"]
