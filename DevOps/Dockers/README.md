Our CICD is set up for multiple build phases. ./Dockerfile_build is for
compiling and run unit tests.

./Dockerfile_run is prep with dependencies to run on AWS.

./Dockerfile copy all the artifacts from the compling stage in Codepipeline
and add it the base runtime image.

**Pushing to ECR**

Prerequisite: Setup AWS CLI and configuration

Authenticate with ECR:
```
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 772817959278.dkr.ecr.us-west-2.amazonaws.com
```
Retag your image to match ECR repo:

for Docker_build
```
docker tag $TheNameOfYourDocker 772817959278.dkr.ecr.us-west-2.amazonaws.com/ubuntu_rust_build:lendable-dsl
```
for ./Dockerfile_run
```
docker tag $TheNameOfYourDocker 772817959278.dkr.ecr.us-west-2.amazonaws.com/ubuntu_rust_run:lendable-dsl
```
Push to ECR:

for Docker_build
```
docker push 7772817959278.dkr.ecr.us-west-2.amazonaws.com/ubuntu_rust_build:lendable-dsl
```
for ./Dockerfile_run
```
docker push 772817959278.dkr.ecr.us-west-2.amazonaws.com/ubuntu_rust_run:lendable-dsl
```
