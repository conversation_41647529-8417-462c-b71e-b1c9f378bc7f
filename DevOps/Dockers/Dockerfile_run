FROM ubuntu:18.04
ENV DEBIAN_FRONTEND noninteractive
RUN apt-get update && apt-get install -y sudo \
                                         build-essential \
                                         findutils \
                                         openssh-client dnsutils libtool \
                                         python3-pip \
                                         wget \
                                         lsb-release \
                                         libpq-dev  \
                                         curl \
                                         unzip \
                                         gnupg2 \
                                         openssl \
                                         libssl-dev && \
                                         pip3 install boto3 && \
                                         curl -sL https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip -o awscliv2.zip && \
                                         unzip awscliv2.zip && \
                                         ./aws/install


#rust
RUN curl https://sh.rustup.rs -sSf | sh -s -- -y

RUN wget http://launchpadlibrarian.net/266559200/wait-for-it_0.0~git20160501-1_all.deb && \
    apt install ./wait-for-it_0.0~git20160501-1_all.deb

ENV PATH /root/.cargo/bin:$PATH
RUN adduser --shell /sbin/nologin --home /home/<USER>
