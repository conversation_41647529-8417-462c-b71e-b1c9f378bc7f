use reqwest;
use serde_json::json;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    
    // Test Arkansas business case (should cap at 0)
    let arkansas_business = json!({
        "loan": {
            "country": "us",
            "province": "ar",
            "type": "business",
            "amount": "30000",
            "amountToCustomer": "30000",
            "loanInterestType": "pni"
        },
        "query": "fee"
    });
    
    println!("Testing Arkansas business case (should cap at 0):");
    let response = client
        .post("http://localhost:13011/v0/legality")
        .json(&arkansas_business)
        .send()
        .await?;
    
    let result: serde_json::Value = response.json().await?;
    println!("Response: {}", serde_json::to_string_pretty(&result)?);
    
    // Test Arkansas personal case (should not cap)
    let arkansas_personal = json!({
        "loan": {
            "country": "us",
            "province": "ar",
            "type": "personal",
            "amount": "25000",
            "amountToCustomer": "25000",
            "loanInterestType": "pni"
        },
        "query": "fee"
    });
    
    println!("\nTesting Arkansas personal case (should not cap):");
    let response = client
        .post("http://localhost:13011/v0/legality")
        .json(&arkansas_personal)
        .send()
        .await?;
    
    let result: serde_json::Value = response.json().await?;
    println!("Response: {}", serde_json::to_string_pretty(&result)?);
    
    // Test Arizona case (should cap at 500)
    let arizona = json!({
        "loan": {
            "country": "us",
            "province": "az",
            "type": "personal",
            "amount": "20000",
            "amountToCustomer": "20000",
            "loanInterestType": "pni"
        },
        "query": "fee"
    });
    
    println!("\nTesting Arizona case (should cap at 500):");
    let response = client
        .post("http://localhost:13011/v0/legality")
        .json(&arizona)
        .send()
        .await?;
    
    let result: serde_json::Value = response.json().await?;
    println!("Response: {}", serde_json::to_string_pretty(&result)?);
    
    Ok(())
}
