#!/bin/bash
# /opt/app/deploy-scripts/transform_template.py /opt/app/config/config.json.template /opt/app/config/config.json
# chown rust:rust /opt/app/config/config.json
# chmod 644 /opt/app/config/config.json
# /opt/app/deploy-scripts/copy_param_to_file.py --key=True /opt/app/config/certificates/private-key.pem
# chown rust:rust /opt/app/config/certificates/private-key.pem
# chmod 600 /opt/app/config/certificates/private-key.pem
# /opt/app/deploy-scripts/copy_param_to_file.py --key=True /opt/app/config/certificates/public-keys/borrower-api.pem
# chown rust:rust /opt/app/config/certificates/public-keys/borrower-api.pem
# chmod 600 /opt/app/config/certificates/public-keys/borrower-api.pem
# /opt/app/deploy-scripts/copy_param_to_file.py --key=True /opt/app/config/certificates/public-keys/admin-api.pem
# chown rust:rust /opt/app/config/certificates/public-keys/admin-api.pem
# chmod 600 /opt/app/config/certificates/public-keys/admin-api.pem
# /opt/app/deploy-scripts/copy_param_to_file.py --key=True /opt/app/config/certificates/public-keys/microservice.pem
# chown rust:rust /opt/app/config/certificates/public-keys/microservice.pem
# chmod 600 /opt/app/config/certificates/public-keys/microservice.pem
# /opt/app/deploy-scripts/copy_param_to_file.py --key=True /opt/app/config/certificates/public-keys/third-party-api.pem
# chown rust:rust /opt/app/config/certificates/public-keys/third-party-api.pem
# chmod 600 /opt/app/config/certificates/public-keys/third-party-api.pem
"$@"
