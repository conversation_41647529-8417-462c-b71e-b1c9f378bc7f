# Lendable-DSL – Local Run Guide

This file is a concise, authoritative reference for developers who need to compile the DSL rules, start the solver service, and run the automated test-suite.

---

## 1 Compile DSL → ASP

Directory: `compiler/`

```bash
# from project root
cd compiler
cargo run -- ../authoritative-rules.lnd
```

This parses _authoritative-rules.lnd_ and overwrites:

- `formulation/rules.lp`
- `formulation/formulation.lp`
- `formulation/metadata.cbor`

---

## 2 Run the Solver HTTP API

Directory: `solver/`

```bash
cd solver
cargo run -- 13011          # 13011 = port number
```

Key endpoints (JSON):
| Method | Path | Purpose |
| ------ | --------------------- | ------------------------------------ |
| GET | `/status/shallow` | Health check (always 200) |
| POST | `/v0/legality` | Main query `{ loan, query }` |
| GET | `/v0/all` | Dump of cached lendability map |

Example query:

```bash
curl -X POST localhost:13011/v0/legality -H 'content-type: application/json' \
     -d '{"loan":{"country":"us","province":"ga","type":"business","amount":"20000","amountToCustomer":"20000"},"query":"fee"}'
```

---

## 3 Run the Automated Tests

From **anywhere** in the repo:

```bash
cargo test -- --nocapture            # full suite
cargo test fee_matrix -- --nocapture # just the fee-matrix cases
```

The test harness automatically:

1. Re-compiles the DSL rules.
2. Symlinks `formulation/` into a temp dir.
3. Starts the solver on a random port.
4. Sends JSON requests and asserts responses.

---

## 4 All-in-one Rebuild Shortcut

```bash
# kill any background solvers (ignore errors)
pkill -f target/debug/solver 2>/dev/null || true

# compile rules & run tests
(cd compiler && cargo run -- ../authoritative-rules.lnd)
(cd solver   && cargo test -- --nocapture)
```

---

## 5 Docker Build (matches CI)

```bash
docker build -f DevOps/Dockers/Dockerfile_build -t lendable-dsl-build .
docker run -it --rm lendable-dsl-build
```

---

### TL;DR

```bash
./build.sh                         # full build (compiler + solver)
./solver/target/debug/solver 13011 # start API
```
