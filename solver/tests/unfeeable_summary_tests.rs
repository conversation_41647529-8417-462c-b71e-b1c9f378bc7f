// Summary test file demonstrating key UNFEEABLE rule test cases
// This file contains representative tests for each major UNFEEABLE pattern

use serde_json::json;

// Import shared test utilities
mod test_constants;
use test_constants::*;

#[cfg(test)]
mod summary_tests {
    use super::*;

    /// Test Pattern 1: Simple fee threshold (Arizona)
    /// UNFEEABLE WHEN fee > 500 → Cap at 500
    #[tokio::test]
    async fn pattern_simple_fee_threshold() {
        let response = post_json(
            "/v0/legality",
            &create_loan_request("az", "personal", Some("20000"), Some("20000"), None, "fee"),
        )
        .await;

        assert_eq!(fee_constraint(0.0, 500.0), response);
    }

    /// Test Pattern 2: Fee + amountToCustomer threshold (Arkansas)
    /// UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k → Cap at 0 when amountToCustomer > 10k
    #[tokio::test]
    async fn pattern_fee_plus_amount_to_customer() {
        // Should cap when amountToCustomer > 10k
        let response_cap = post_json(
            "/v0/legality",
            &create_loan_request("ar", "business", Some("30000"), Some("15000"), None, "fee"),
        )
        .await;
        assert_eq!(fee_constraint(0.0, 0.0), response_cap);

        // Should NOT cap when amountToCustomer <= 10k
        let response_no_cap = post_json(
            "/v0/legality",
            &create_loan_request("ar", "business", Some("30000"), Some("8000"), None, "fee"),
        )
        .await;
        assert_ne!(fee_constraint(0.0, 0.0), response_no_cap);
    }

    /// Test Pattern 3: Fee + amount threshold (Georgia)
    /// UNFEEABLE WHEN fee > 0 AND amount > 15k → Cap at 0 when amount > 15k
    #[tokio::test]
    async fn pattern_fee_plus_amount() {
        // Should cap when amount > 15k
        let response_cap = post_json(
            "/v0/legality",
            &create_loan_request("ga", "business", Some("20000"), Some("20000"), None, "fee"),
        )
        .await;
        assert_eq!(fee_constraint(0.0, 0.0), response_cap);

        // Should NOT cap when amount <= 15k
        let response_no_cap = post_json(
            "/v0/legality",
            &create_loan_request("ga", "business", Some("10000"), Some("10000"), None, "fee"),
        )
        .await;
        assert_ne!(fee_constraint(0.0, 0.0), response_no_cap);
    }

    /// Test Pattern 4: Higher fee threshold (Colorado)
    /// UNFEEABLE WHEN fee > 100 AND amountToCustomer > 30k → Cap at 100 when amountToCustomer > 30k
    #[tokio::test]
    async fn pattern_higher_fee_threshold() {
        // Should cap at 100 when amountToCustomer > 30k
        let response_cap = post_json(
            "/v0/legality",
            &create_loan_request("co", "personal", Some("50000"), Some("35000"), None, "fee"),
        )
        .await;
        assert_eq!(fee_constraint(0.0, 100.0), response_cap);

        // Should NOT cap when amountToCustomer <= 30k
        let response_no_cap = post_json(
            "/v0/legality",
            &create_loan_request("co", "personal", Some("50000"), Some("25000"), None, "fee"),
        )
        .await;
        assert_ne!(fee_constraint(0.0, 100.0), response_no_cap);
    }

    /// Test Pattern 5: Reverse condition (Kentucky)
    /// UNFEEABLE WHEN fee > 300 AND amountToCustomer < 69.5k → Cap at 300 when amountToCustomer < 69.5k
    #[tokio::test]
    async fn pattern_reverse_condition() {
        // Should cap at 300 when amountToCustomer < 69.5k
        let response_cap = post_json(
            "/v0/legality",
            &create_loan_request("ky", "business", Some("50000"), Some("50000"), None, "fee"),
        )
        .await;
        assert_eq!(fee_constraint(0.0, 300.0), response_cap);

        // Should NOT cap when amountToCustomer >= 69.5k
        let response_no_cap = post_json(
            "/v0/legality",
            &create_loan_request("ky", "business", Some("100000"), Some("80000"), None, "fee"),
        )
        .await;
        assert_ne!(fee_constraint(0.0, 300.0), response_no_cap);
    }

    /// Test Pattern 6: Always unfeeable (New Jersey)
    /// UNFEEABLE WHEN fee > 0 → Always cap at 0
    #[tokio::test]
    async fn pattern_always_unfeeable() {
        let response = post_json(
            "/v0/legality",
            &create_loan_request("nj", "business", Some("50000"), Some("50000"), None, "fee"),
        )
        .await;

        assert_eq!(fee_constraint(0.0, 0.0), response);
    }

    /// Test Legality: Legal case with fee at threshold
    #[tokio::test]
    async fn legality_at_threshold() {
        // Colorado: fee = 100 should be legal when amountToCustomer > 30k
        let mut loan_request = create_loan_request("co", "personal", Some("50000"), Some("35000"), None, "legality");
        loan_request["loan"]["fee"] = json!("100");
        
        let response = post_json("/v0/legality", &loan_request).await;
        assert_eq!(LENDABLE, response);
    }

    /// Test Legality: Illegal case with fee above threshold
    #[tokio::test]
    async fn legality_above_threshold() {
        // Colorado: fee = 150 should be illegal when amountToCustomer > 30k
        let mut loan_request = create_loan_request("co", "personal", Some("50000"), Some("35000"), None, "legality");
        loan_request["loan"]["fee"] = json!("150");
        
        let response = post_json("/v0/legality", &loan_request).await;
        assert_eq!(NOT_LENDABLE, response);
    }

    /// Test Boundary: Exact threshold values
    #[tokio::test]
    async fn boundary_exact_threshold() {
        // Arkansas: amountToCustomer = 10000 exactly should NOT trigger UNFEEABLE
        let response = post_json(
            "/v0/legality",
            &create_loan_request("ar", "business", Some("30000"), Some("10000"), None, "fee"),
        )
        .await;

        assert_ne!(fee_constraint(0.0, 0.0), response);
    }

    /// Test Cross-Parameter: amount vs amountToCustomer
    #[tokio::test]
    async fn cross_parameter_distinction() {
        // Georgia uses 'amount', not 'amountToCustomer'
        // amount > 15k, amountToCustomer <= 15k should trigger UNFEEABLE
        let response = post_json(
            "/v0/legality",
            &create_loan_request("ga", "business", Some("20000"), Some("10000"), None, "fee"),
        )
        .await;
        assert_eq!(fee_constraint(0.0, 0.0), response);
    }

    /// Test No Cap: Conditions not met
    #[tokio::test]
    async fn no_cap_conditions_not_met() {
        // Arkansas: UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k
        // Should NOT cap when amountToCustomer <= 10k
        let response = post_json(
            "/v0/legality",
            &create_loan_request("ar", "business", Some("30000"), Some("8000"), None, "fee"),
        )
        .await;

        // Should return normal fee constraints, not UNFEEABLE cap
        if let serde_json::Value::Array(constraints) = &response {
            assert!(constraints.len() >= 1);
            // Should have normal fee constraints, not capped at 0
            let max_constraint = constraints.iter().find(|c| {
                c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
            });

            if let Some(max_constraint) = max_constraint {
                let max_value = max_constraint.get("value")
                    .and_then(|v| v.as_str())
                    .and_then(|s| s.parse::<f64>().ok())
                    .unwrap_or(0.0);
                // Max fee should be significantly higher than 0 (normal fee constraints)
                assert!(max_value > 100.0, "Expected normal fee constraints, got max fee: {}", max_value);
            }
        } else {
            panic!("Expected array of constraints, got: {:?}", response);
        }
    }

    /// Test No Cap: State without UNFEEABLE rules
    #[tokio::test]
    async fn no_cap_no_unfeeable_rules() {
        // California has no UNFEEABLE rules - should always return normal fee constraints
        let response = post_json(
            "/v0/legality",
            &create_loan_request("ca", "personal", Some("50000"), Some("50000"), None, "fee"),
        )
        .await;

        // Should return normal fee constraints
        if let serde_json::Value::Array(constraints) = &response {
            assert!(constraints.len() >= 1);

            let has_gte = constraints.iter().any(|c| {
                c.get("constraint").and_then(|v| v.as_str()) == Some("GTE")
            });
            let has_lte = constraints.iter().any(|c| {
                c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
            });

            assert!(has_gte, "Should have GTE constraint for minimum fee");
            assert!(has_lte, "Should have LTE constraint for maximum fee");

            // Check that max fee is reasonable (not artificially capped)
            let max_constraint = constraints.iter().find(|c| {
                c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
            });

            if let Some(max_constraint) = max_constraint {
                let max_value = max_constraint.get("value")
                    .and_then(|v| v.as_str())
                    .and_then(|s| s.parse::<f64>().ok())
                    .unwrap_or(0.0);
                // Should have a reasonable max fee, not artificially low
                assert!(max_value > 1000.0, "Expected normal fee constraints, got max fee: {}", max_value);
            }
        } else {
            panic!("Expected array of constraints, got: {:?}", response);
        }
    }

    /// Test Comparison: Cap vs No Cap
    #[tokio::test]
    async fn comparison_cap_vs_no_cap() {
        // Colorado: UNFEEABLE WHEN fee > 100 AND amountToCustomer > 30k

        // Case 1: Should cap (amountToCustomer > 30k)
        let response_cap = post_json(
            "/v0/legality",
            &create_loan_request("co", "personal", Some("50000"), Some("35000"), None, "fee"),
        )
        .await;

        // Case 2: Should NOT cap (amountToCustomer <= 30k)
        let response_no_cap = post_json(
            "/v0/legality",
            &create_loan_request("co", "personal", Some("50000"), Some("25000"), None, "fee"),
        )
        .await;

        // Verify they are different
        assert_ne!(response_cap, response_no_cap);

        // Verify cap case has fee constraint at 100
        assert_eq!(fee_constraint(0.0, 100.0), response_cap);

        // Verify no-cap case has normal fee constraints (much higher max)
        if let serde_json::Value::Array(constraints) = &response_no_cap {
            let max_constraint = constraints.iter().find(|c| {
                c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
            });

            if let Some(max_constraint) = max_constraint {
                let max_value = max_constraint.get("value")
                    .and_then(|v| v.as_str())
                    .and_then(|s| s.parse::<f64>().ok())
                    .unwrap_or(0.0);
                assert!(max_value > 200.0, "Expected normal fee constraints, got max fee: {}", max_value);
            }
        }
    }
}
