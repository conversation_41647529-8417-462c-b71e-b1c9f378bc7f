use serde_json::{json, Value};
use std::sync::{Arc, Mutex};
use std::time::Duration;
use tokio::time::delay_for;
use duct::cmd;
use lazy_static::lazy_static;
use reqwest;

// Source: "Appendix B, Two–Letter State and Possession Abbreviations"
//          https://pe.usps.com/text/pub28/28apb.htm
pub const US_STATE_AND_POSSESSION_ABBREVS: [&str; 59] = [
    "as", // American Somoa
    "dc", // District of Columbia
    "fm", // Federated States of Micronesia
    "gu", // Guam
    "mh", // Marshall Islands
    "mp", // Northern Mariana Islands
    "pw", // Palau
    "pr", // Puerto Rico
    "vi", // Virgin Islands
    // States
    "al", "ak", "az", "ar", "ca", "co", "ct", "de", "fl", "ga", "hi", "id", "il", "in", "ia", "ks",
    "ky", "la", "me", "md", "ma", "mi", "mn", "ms", "mo", "mt", "ne", "nv", "nh", "nj", "nm", "ny",
    "nc", "nd", "oh", "ok", "or", "pa", "ri", "sc", "sd", "tn", "tx", "ut", "vt", "va", "wa", "wv",
    "wi", "wy",
];

// Test server configuration
pub const TEST_PORT: &str = "13001";
pub const TEST_URL_BASE: &str = "http://localhost";

// API responses for success and failure
pub const LENDABLE: Value = json!([]);
pub const NOT_LENDABLE: Value = json!(null);

lazy_static! {
    pub static ref SERVER: Arc<Mutex<duct::ReaderHandle>> = {
        println!("Building for UNFEEABLE tests");
        cmd!("cargo", "run", "--", "../authoritative-rules.lnd")
            .dir("../compiler")
            .run()
            .expect("Being able to compile");

        println!("Linking built for UNFEEABLE tests");
        cmd!("ln", "-sf", "../formulation/", ".")
            .run()
            .expect("Making a link of the formulations");

        println!("Starting server for UNFEEABLE tests");
        let server = cmd!("cargo", "run", "--", TEST_PORT)
            .reader()
            .expect("Starting the server");

        Arc::new(Mutex::new(server))
    };
}

pub fn test_url(path: &str) -> String {
    format!("{}:{}{}", TEST_URL_BASE, TEST_PORT, path)
}

pub async fn wait_for_server() {
    lazy_static::initialize(&SERVER);
    for i in 0..(1000 as usize) {
        if let Ok(status) = reqwest::get(&test_url("/status/shallow"))
            .await
            .map(|x| x.status())
        {
            if status == reqwest::StatusCode::OK {
                break;
            }
        }
        delay_for(Duration::from_millis(10)).await;
    }
}

pub async fn post_json(path: &str, json: &Value) -> Value {
    wait_for_server().await;
    reqwest::Client::new()
        .post(&test_url(path))
        .json(json)
        .send()
        .await
        .expect("Valid response")
        .json()
        .await
        .expect("Valid JSON")
}

// Helper function to create fee constraint expectation
pub fn fee_constraint(min: f64, max: f64) -> Value {
    json!([
        {
            "constraint": "GTE",
            "value": min.to_string()
        },
        {
            "constraint": "LTE",
            "value": max.to_string()
        }
    ])
}

// Helper function to create loan request
pub fn create_loan_request(
    province: &str,
    loan_type: &str,
    amount: Option<&str>,
    amount_to_customer: Option<&str>,
    apr: Option<&str>,
    query: &str,
) -> Value {
    let mut loan = json!({
        "country": "us",
        "province": province,
        "type": loan_type,
        "loanInterestType": "pni"
    });

    if let Some(amt) = amount {
        loan["amount"] = json!(amt);
    }
    if let Some(atc) = amount_to_customer {
        loan["amountToCustomer"] = json!(atc);
    }
    if let Some(a) = apr {
        loan["apr"] = json!(a);
    }

    json!({
        "loan": loan,
        "query": query
    })
}
