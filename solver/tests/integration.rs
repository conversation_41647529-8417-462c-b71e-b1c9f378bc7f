use duct::cmd;
use lazy_static::lazy_static;
use locale_codes::country;
use pretty_assertions::assert_eq;
use serde_json::{json, Map, Value};
use std::sync::{Arc, Mutex};
use std::{fmt::Debug, time::Duration};
use tokio::time::delay_for;

mod test_constants;
mod unfeeable_tests;

// Test server configuration
const TEST_PORT: &str = "13000";
const TEST_URL_BASE: &str = "http://localhost";

// API responses for success and failure
const LENDABLE: Value = json!([]);
const NOT_LENDABLE: Value = json!(null);

lazy_static! {
    static ref SERVER: Arc<Mutex<duct::ReaderHandle>> = {
        println!("Building");
        cmd!("cargo", "run", "--", "../authoritative-rules.lnd")
            .dir("../compiler")
            .run()
            .expect("Being able to compile");

        println!("Linking built");
        cmd!("ln", "-sf", "../formulation/", ".")
            .run()
            .expect("Making a link of the formulations");

        println!("Starting server");
        Arc::new(Mutex::new(
            cmd!("cargo", "run", "--", TEST_PORT)
                .reader()
                .expect("Valid server"),
        ))
    };
}

async fn wait_for_server() {
    lazy_static::initialize(&SERVER);
    for i in 0..(1000 as usize) {
        if let Ok(status) = reqwest::get(&test_url("/status/shallow"))
            .await
            .map(|x| x.status())
        {
            if status == reqwest::StatusCode::OK {
                break;
            }
        }
        if i >= 999 {
            panic!("We have waited too long for the server to start");
        }
        delay_for(Duration::from_millis(100)).await;
    }
}

#[tokio::test]
async fn integration_test_query_amount() {
    let response = post_json(
        "/v0/legality",
        &json!({
          "loan": {
            "country": "us",
            "province": "co",
            "type": "personal"
          },
          "query": "amount"
        }),
    )
    .await;

    assert_eq!(
        json!([
          {
            "constraint": "GT",
            "value": "5000"
          },
          {
            "constraint": "LTE",
            "value": "100000000"
          }
        ]),
        response
    );
}

#[tokio::test]
async fn integration_test_query_amount_too_high_is_rejected() {
    let response = post_json(
        "/v0/legality",
        &json!(
        {
          "loan": {
            "country": "us",
            "province": "az",
            "type": "personal",
            "amount": "200000000",  // Exceeds global max $100,000,000
            "loanInterestType": "pni"
          }
        }),
    )
    .await;

    assert_eq!(NOT_LENDABLE, response);
}

#[tokio::test]
async fn integration_test_query_valid_loan_interest_type() {
    let response = post_json(
        "/v0/legality",
        &json!(
        {
          "loan": {
            "country": "us",
            "province": "az",
            "type": "business",
            "loanInterestType": "pni"
          }
        }),
    )
    .await;

    assert_eq!(LENDABLE, response);
}

#[tokio::test]
async fn integration_test_query_invalid_loan_interest_type() {
    let response = post_json(
        "/v0/legality",
        &json!(
        {
          "loan": {
            "country": "us",
            "province": "az",
            "type": "personal",
            "amount": "10000",
            "loanInterestType": "io"
          }
        }),
    )
    .await;

    assert_eq!(NOT_LENDABLE, response);
}

#[tokio::test]
async fn integration_test_query_invalid_loan_interest_type_2() {
    let response = post_json(
        "/v0/legality",
        &json!(
        {
          "loan": {
            "country": "us",
            "province": "vt",
            "type": "business",
            "loanInterestType": "io"
          }
        }),
    )
    .await;

    assert_eq!(NOT_LENDABLE, response);
}

#[tokio::test]
async fn integration_test_query_apr() {
    let response = post_json(
        "/v0/legality",
        &json!({
          "loan": {
            "country": "us",
            "province": "co",
            "type": "personal",
            "amount": "7000"
          },
          "query": "apr"
        }),
    )
    .await;

    assert_eq!(
        json!([
          {
            "constraint": "GTE",
            "value": "0"
          },
          {
            "constraint": "LTE",
            "value": "0.45"
          }
        ]),
        response
    );
}

#[tokio::test]
async fn integration_test_query_term() {
    let response = post_json(
        "/v0/legality",
        &json!({
          "loan": {
            "country": "us",
            "province": "co",
            "type": "personal",
            "amount": "7000"
          },
          "query": "term"
        }),
    )
    .await;

    assert_eq!(
        json!([
          {
            "constraint": "GTE",
            "value": "7776000000"
          },
          {
            "constraint": "LTE",
            "value": "155520000000"
          }
        ]),
        response
    );
}

#[tokio::test]
async fn integration_test_legal() {
    let response = post_json(
        "/v0/legality",
        &json!({
          "loan": {
            "country": "us",
            "province": "co",
            "type": "personal",
            "amount": "7000",
            "apr": "0.15"
          }
        }),
    )
    .await;

    assert_eq!(LENDABLE, response);
}
#[tokio::test]
async fn integration_test_illegal() {
    let response = post_json(
        "/v0/legality",
        &json!({
          "loan": {
            "country": "us",
            "province": "co",
            "type": "personal",
            "amount": "70000",
            "apr": "0.50"
          }
        }),
    )
    .await;

    assert_eq!(NOT_LENDABLE, response);
}

#[tokio::test]
async fn integration_test_madagascar_should_not_be_legal() {
    let response = post_json(
        "/v0/legality",
        &json!({
          "loan": {
            "country": "mg",
            "province": "d",
            "type": "personal",
            "amount": "7000",
            "apr": "0.15"
          }
        }),
    )
    .await;

    assert_eq!(NOT_LENDABLE, response);
}

//
// Schema Tests
//

const TEST_COUNTRY: &str = "zz";
#[tokio::test]
async fn integration_test_schema_country_codes() {
    let json = get_json("/v0/all").await;
    let country_codes = json.keys();

    assert_all(country_codes, |cc| {
        is_valid_country_code(cc) || cc == &TEST_COUNTRY
    });
}

#[tokio::test]
async fn integration_test_schema_province_codes() {
    let json = get_json("/v0/all").await;
    let province_codes = json
        .values()
        .flat_map(|country| country.as_object().unwrap().keys());

    assert_all(province_codes, |rc| {
        is_valid_province_code(rc) || rc == &"default"
    });
}

#[tokio::test]
async fn integration_test_schema_province_values() {
    let json = get_json("/v0/all").await;
    let province_values = json
        .values()
        .flat_map(|country| country.as_object().unwrap().values())
        .map(|value| value.as_str().unwrap())
        .collect::<Vec<_>>();

    assert_all(province_values, is_valid_province_value);
}

//
// Helper functions
//

fn assert_all<A>(xs: impl IntoIterator<Item = A>, predicate: fn(A) -> bool)
where
    A: Debug + Copy,
{
    let unknown_values = xs.into_iter().filter(|&item| !predicate(item));

    assert_empty(unknown_values, Some("is unknown"));
}

fn assert_empty(xs: impl IntoIterator<Item = impl Debug>, error_msg: Option<&str>) {
    let xs = xs.into_iter().collect::<Vec<_>>();
    let message = error_msg.unwrap_or("should be empty");

    assert!(xs.is_empty(), "{:?} {}", xs, message);
}

async fn post_json(abs_path: &str, value: &Value) -> Value {
    wait_for_server().await;

    let client = reqwest::Client::new();
    let response: Value = client
        .post(&test_url(abs_path))
        .json(value)
        .send()
        .await
        .unwrap()
        .json()
        .await
        .unwrap();

    response.to_owned()
}

async fn get_json(abs_path: &str) -> Map<String, Value> {
    wait_for_server().await;

    let client = reqwest::Client::new();
    let response: Value = client
        .get(&test_url(abs_path))
        .send()
        .await
        .unwrap()
        .json()
        .await
        .unwrap();
    let json = response.as_object().unwrap();

    json.to_owned()
}

fn is_valid_country_code(cc: &str) -> bool {
    let cleaned_up_code = cc.to_ascii_uppercase();
    country::lookup(&cleaned_up_code).is_some()
}

fn is_valid_province_code(rc: &str) -> bool {
    test_constants::US_STATE_AND_POSSESSION_ABBREVS.contains(&rc)
}

fn is_valid_province_value(value: &str) -> bool {
    ["business_only", "lendable", "non_lendable"].contains(&value)
}

fn test_url(absolute_path: &str) -> String {
    let base_url = &format!("{}:{}", TEST_URL_BASE, TEST_PORT);

    format!("{}{}", base_url, absolute_path)
}
