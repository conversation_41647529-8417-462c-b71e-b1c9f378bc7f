use crate::LoanInterestType;
pub use crate::LoanType;
use crate::{num::NUM, Alpha2};
use serde::Deserialize;

use crate::salt::subdivision_code::SubdivisionCode;

#[derive(Deserialize, Debug, PartialEq, Eq, Hash, Clone)]
#[serde(rename_all = "camelCase")]
pub struct LoanInstance {
    pub country: Alpha2,
    pub province: Option<SubdivisionCode>,
    #[serde(rename = "type")]
    pub loan_type: LoanType,
    pub loan_interest_type: Option<LoanInterestType>,
    pub amount: Option<NUM>,
    pub apr: Option<NUM>,
    pub term: Option<NUM>,
    pub fee: Option<NUM>,
    pub amount_to_customer: Option<NUM>,
}
