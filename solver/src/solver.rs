use crate::loan_instance::LoanInstance;
use crate::solve_type::SolveType;
use crate::{
    to_amount_int, to_amt_int, to_apr_int, to_fee_int, to_amount_to_customer_int, ConstraintType, QueryType, ValueConstraint,
    AMOUNT_TREE, APR_TREE, FEE_TREE, FORMU<PERSON><PERSON>ON, MS_PER_MONTH, POSSIBLE_VALUES, RULES, TERM_TREE,
};
use anyhow::{format_err, Error, Result};
use clingo::*;
use fraction::Fraction as BigFrac;
use std::sync::mpsc::{sync_channel, Receiver, SyncSender};

pub struct Solver {
    sender: SyncSender<(SolveType, LoanInstance, QueryType)>,
    receiver: Receiver<Result<Option<ValueConstraint>, Error>>,
}

impl Solver {
    pub fn solve(
        &mut self,
        solve_type: SolveType,
        loan_instance: LoanInstance,
        query_type: QueryType,
    ) -> Result<Option<ValueConstraint>, Error> {
        self.sender.send((solve_type, loan_instance, query_type))?;
        self.receiver.recv()?
    }
}
fn add_symbol(ctl: &mut Control, symbol: Symbol, truth_value: TruthValue) -> Result<()> {
    let atoms = ctl.symbolic_atoms()?;
    let mut atm_it = atoms.iter()?;
    match atm_it.find(|e| e.symbol().unwrap() == symbol) {
        Some(item) => {
            let atm = item.literal()?;
            ctl.assign_external(atm, truth_value)?;
        }
        None => {
            // symbol not present; ignore
        }
    }
    Ok(())
}

impl Default for Solver {
    fn default() -> Solver {
        let (worker_in_send, worker_in_rec) =
            sync_channel::<(SolveType, LoanInstance, QueryType)>(0);
        let (worker_out_send, worker_out_rec) =
            sync_channel::<Result<Option<ValueConstraint>, Error>>(0);
        std::thread::spawn(move || {
            let mut ctl_pool_sat: Vec<Control> = Vec::new();
            let mut ctl_pool_min_apr: Vec<Control> = Vec::new();
            let mut ctl_pool_max_apr: Vec<Control> = Vec::new();
            let mut ctl_pool_min_amount: Vec<Control> = Vec::new();
            let mut ctl_pool_max_amount: Vec<Control> = Vec::new();
            let mut ctl_pool_min_term: Vec<Control> = Vec::new();
            let mut ctl_pool_max_term: Vec<Control> = Vec::new();
            let mut ctl_pool_min_fee: Vec<Control> = Vec::new();
            let mut ctl_pool_max_fee: Vec<Control> = Vec::new();
            let mut solve_instance = |solve_type,
                                      instance: LoanInstance,
                                      query: QueryType|
             -> Result<Option<ValueConstraint>, Error> {
                let mut ctl = match solve_type {
                    SolveType::SAT => ctl_pool_sat.pop(),
                    SolveType::MIN_APR => ctl_pool_min_apr.pop(),
                    SolveType::MAX_APR => ctl_pool_max_apr.pop(),
                    SolveType::MIN_AMOUNT => ctl_pool_min_amount.pop(),
                    SolveType::MAX_AMOUNT => ctl_pool_max_amount.pop(),
                    SolveType::MIN_TERM => ctl_pool_min_term.pop(),
                    SolveType::MAX_TERM => ctl_pool_max_term.pop(),
                    SolveType::MIN_FEE => ctl_pool_min_fee.pop(),
                    SolveType::MAX_FEE => ctl_pool_max_fee.pop(),
                }
                .ok_or_else(|| format_err!("none"))
                .or_else(|_| produce_base_program(solve_type))?;

                match instance.amount {
                    Some(a) => {
                        if let QueryType::Fee = query {
                            add_symbol(
                                &mut ctl,
                                Symbol::create_function(
                                    "amt_ext",
                                    &[Symbol::create_number(to_amt_int(a.0))],
                                    true,
                                )?,
                                TruthValue::True,
                            )?
                        } else {
                            add_symbol(
                                &mut ctl,
                                Symbol::create_id("choose_amt", true)?,
                                TruthValue::True,
                            )?
                        }

                        add_symbol(
                            &mut ctl,
                            Symbol::create_function(
                                "amount_ext",
                                &[Symbol::create_number(to_amount_int(a.0))],
                                true,
                            )?,
                            TruthValue::True,
                        )?
                    }
                    None => {
                        add_symbol(
                            &mut ctl,
                            Symbol::create_id("choose_amt", true)?,
                            TruthValue::True,
                        )?;

                        add_symbol(
                            &mut ctl,
                            Symbol::create_id("choose_amount", true)?,
                            TruthValue::True,
                        )?;
                    },
                };
                match instance.apr {
                    Some(a) => add_symbol(
                        &mut ctl,
                        Symbol::create_function(
                            "apr_ext",
                            &[Symbol::create_number(to_apr_int(a.0))],
                            true,
                        )?,
                        TruthValue::True,
                    )?,
                    None => add_symbol(
                        &mut ctl,
                        Symbol::create_id("choose_apr", true)?,
                        TruthValue::True,
                    )?,
                };
                match instance.term {
                    Some(a) => add_symbol(
                        &mut ctl,
                        Symbol::create_function(
                            "term_ext",
                            &[Symbol::create_number(to_term_int(a.0))],
                            true,
                        )?,
                        TruthValue::True,
                    )?,
                    None => add_symbol(
                        &mut ctl,
                        Symbol::create_id("choose_term", true)?,
                        TruthValue::True,
                    )?,
                };
                match instance.fee {
                    Some(a) => add_symbol(
                        &mut ctl,
                        Symbol::create_function(
                            "fee_ext",
                            &[Symbol::create_number(to_fee_int(a.0))],
                            true,
                        )?,
                        TruthValue::True,
                    )?,
                    None => add_symbol(
                        &mut ctl,
                        Symbol::create_id("choose_fee", true)?,
                        TruthValue::True,
                    )?,
                };
                if let Some(a) = &instance.amount_to_customer {
                    add_symbol(
                        &mut ctl,
                        Symbol::create_function(
                            "amount_to_customer_ext",
                            &[Symbol::create_number(to_amount_to_customer_int(a.0))],
                            true,
                        )?,
                        TruthValue::True,
                    )?
                }
                add_symbol(
                    &mut ctl,
                    Symbol::create_function(
                        "country",
                        &[Symbol::create_id(&format!("{}", instance.country), true)?],
                        true,
                    )?,
                    TruthValue::True,
                )?;
                if let Some(a) = &instance.province {
                    add_symbol(
                        &mut ctl,
                        Symbol::create_function(
                            "province",
                            &[Symbol::create_string(&format!("{}", a))?],
                            true,
                        )?,
                        TruthValue::True,
                    )?
                }
                if let Some(ref a) = instance.loan_interest_type {
                    add_symbol(
                        &mut ctl,
                        Symbol::create_function(
                            "interest",
                            &[Symbol::create_string(&format!("{}", a))?],
                            true,
                        )?,
                        TruthValue::True,
                    )?
                }
                add_symbol(
                    &mut ctl,
                    Symbol::create_id(&format!("{}", instance.loan_type), true)?,
                    TruthValue::True,
                )?;

                let res = {
                    let mut handle = ctl.solve(SolveMode::YIELD, &[])?;
                    match solve_type {
                        SolveType::SAT => {
                            handle.resume()?;
                            let res = handle.model()?.map(|_original| ValueConstraint::UNK);
                            handle.close()?;
                            res
                        }
                        SolveType::MIN_APR => {
                            let sym_vec_opt = get_optimum(&mut handle)?;
                            handle.close()?;
                            match sym_vec_opt {
                                None => None,
                                Some(sym_vec) => {
                                    let sym_atom = sym_vec
                                        .get(0)
                                        .ok_or_else(|| format_err!("apr not present in model"))?;
                                    let sym_args_vec = sym_atom.arguments()?;
                                    let sym_val = sym_args_vec
                                        .get(0)
                                        .ok_or_else(|| format_err!("apr does not have an arg"))?;
                                    Some(from_apr_int(sym_val.number()?, &ConstraintType::Min))
                                }
                            }
                        }
                        SolveType::MAX_APR => {
                            let sym_vec_opt = get_optimum(&mut handle)?;
                            handle.close()?;
                            match sym_vec_opt {
                                None => None,
                                Some(sym_vec) => {
                                    let sym_atom = sym_vec
                                        .get(0)
                                        .ok_or_else(|| format_err!("apr not present in model"))?;
                                    let sym_args_vec = sym_atom.arguments()?;
                                    let sym_val = sym_args_vec
                                        .get(0)
                                        .ok_or_else(|| format_err!("apr does not have an arg"))?;
                                    Some(from_apr_int(sym_val.number()?, &ConstraintType::Max))
                                }
                            }
                        }
                        SolveType::MIN_AMOUNT => {
                            let sym_vec_opt = get_optimum(&mut handle)?;
                            handle.close()?;
                            match sym_vec_opt {
                                None => None,
                                Some(sym_vec) => {
                                    let sym_atom = sym_vec.get(0).ok_or_else(|| {
                                        format_err!("amount not present in model")
                                    })?;
                                    let sym_args_vec = sym_atom.arguments()?;
                                    let sym_val = sym_args_vec.get(0).ok_or_else(|| {
                                        format_err!("amount does not have an arg")
                                    })?;
                                    Some(from_amount_int(sym_val.number()?, &ConstraintType::Min))
                                }
                            }
                        }
                        SolveType::MAX_AMOUNT => {
                            let sym_vec_opt = get_optimum(&mut handle)?;
                            handle.close()?;
                            match sym_vec_opt {
                                None => None,
                                Some(sym_vec) => {
                                    let sym_atom = sym_vec.get(0).ok_or_else(|| {
                                        format_err!("amount not present in model")
                                    })?;
                                    let sym_args_vec = sym_atom.arguments()?;
                                    let sym_val = sym_args_vec.get(0).ok_or_else(|| {
                                        format_err!("amount does not have an arg")
                                    })?;
                                    Some(from_amount_int(sym_val.number()?, &ConstraintType::Max))
                                }
                            }
                        }
                        SolveType::MIN_TERM => {
                            let sym_vec_opt = get_optimum(&mut handle)?;
                            handle.close()?;
                            match sym_vec_opt {
                                None => None,
                                Some(sym_vec) => {
                                    let sym_atom = sym_vec
                                        .get(0)
                                        .ok_or_else(|| format_err!("term not present in model"))?;
                                    let sym_args_vec = sym_atom.arguments()?;
                                    let sym_val = sym_args_vec
                                        .get(0)
                                        .ok_or_else(|| format_err!("term does not have an arg"))?;
                                    Some(from_term_int(sym_val.number()?, &ConstraintType::Min))
                                }
                            }
                        }
                        SolveType::MAX_TERM => {
                            let sym_vec_opt = get_optimum(&mut handle)?;
                            handle.close()?;
                            match sym_vec_opt {
                                None => None,
                                Some(sym_vec) => {
                                    let sym_atom = sym_vec
                                        .get(0)
                                        .ok_or_else(|| format_err!("term not present in model"))?;
                                    let sym_args_vec = sym_atom.arguments()?;
                                    let sym_val = sym_args_vec
                                        .get(0)
                                        .ok_or_else(|| format_err!("term does not have an arg"))?;
                                    Some(from_term_int(sym_val.number()?, &ConstraintType::Max))
                                }
                            }
                        }
                        SolveType::MIN_FEE => {
                            let sym_vec_opt = get_optimum(&mut handle)?;
                            handle.close()?;
                            match sym_vec_opt {
                                None => None,
                                Some(sym_vec) => {
                                    let sym_atom = sym_vec
                                        .get(0)
                                        .ok_or_else(|| format_err!("fee not present in model"))?;
                                    let sym_args_vec = sym_atom.arguments()?;
                                    let sym_val = sym_args_vec
                                        .get(0)
                                        .ok_or_else(|| format_err!("fee does not have an arg"))?;
                                    Some(from_fee_int(sym_val.number()?, &ConstraintType::Min))
                                }
                            }
                        }
                        SolveType::MAX_FEE => {
                            let sym_vec_opt = get_optimum(&mut handle)?;
                            handle.close()?;
                            match sym_vec_opt {
                                None => None,
                                Some(sym_vec) => {
                                    let sym_atom = sym_vec
                                        .get(0)
                                        .ok_or_else(|| format_err!("fee not present in model"))?;
                                    let sym_args_vec = sym_atom.arguments()?;
                                    let sym_val = sym_args_vec
                                        .get(0)
                                        .ok_or_else(|| format_err!("fee does not have an arg"))?;
                                    Some(from_fee_int(sym_val.number()?, &ConstraintType::Max))
                                }
                            }
                        }
                    }
                };

                match instance.amount {
                    Some(a) => {
                        if let QueryType::Fee = query {
                            add_symbol(
                                &mut ctl,
                                Symbol::create_function(
                                    "amt_ext",
                                    &[Symbol::create_number(to_amt_int(a.0))],
                                    true,
                                )?,
                                TruthValue::False,
                            )?
                        } else {
                            add_symbol(
                                &mut ctl,
                                Symbol::create_id("choose_amt", true)?,
                                TruthValue::False,
                            )?
                        }

                        add_symbol(
                            &mut ctl,
                            Symbol::create_function(
                                "amount_ext",
                                &[Symbol::create_number(to_amount_int(a.0))],
                                true,
                            )?,
                            TruthValue::False,
                        )?
                    }
                    None => {
                        add_symbol(
                            &mut ctl,
                            Symbol::create_id("choose_amt", true)?,
                            TruthValue::False,
                        )?;

                        add_symbol(
                            &mut ctl,
                            Symbol::create_id("choose_amount", true)?,
                            TruthValue::False,
                        )?
                    },
                };
                match instance.apr {
                    Some(a) => add_symbol(
                        &mut ctl,
                        Symbol::create_function(
                            "apr_ext",
                            &[Symbol::create_number(to_apr_int(a.0))],
                            true,
                        )?,
                        TruthValue::False,
                    )?,
                    None => add_symbol(
                        &mut ctl,
                        Symbol::create_id("choose_apr", true)?,
                        TruthValue::False,
                    )?,
                };
                match instance.term {
                    Some(a) => add_symbol(
                        &mut ctl,
                        Symbol::create_function(
                            "term_ext",
                            &[Symbol::create_number(to_term_int(a.0))],
                            true,
                        )?,
                        TruthValue::False,
                    )?,
                    None => add_symbol(
                        &mut ctl,
                        Symbol::create_id("choose_term", true)?,
                        TruthValue::False,
                    )?,
                };
                match instance.fee {
                    Some(a) => add_symbol(
                        &mut ctl,
                        Symbol::create_function(
                            "fee_ext",
                            &[Symbol::create_number(to_fee_int(a.0))],
                            true,
                        )?,
                        TruthValue::False,
                    )?,
                    None => add_symbol(
                        &mut ctl,
                        Symbol::create_id("choose_fee", true)?,
                        TruthValue::False,
                    )?,
                };
                if let Some(a) = &instance.amount_to_customer {
                    add_symbol(
                        &mut ctl,
                        Symbol::create_function(
                            "amount_to_customer_ext",
                            &[Symbol::create_number(to_amount_to_customer_int(a.0))],
                            true,
                        )?,
                        TruthValue::False,
                    )?
                }
                add_symbol(
                    &mut ctl,
                    Symbol::create_function(
                        "country",
                        &[Symbol::create_id(&format!("{}", instance.country), true)?],
                        true,
                    )?,
                    TruthValue::False,
                )?;
                if let Some(a) = instance.province {
                    add_symbol(
                        &mut ctl,
                        Symbol::create_function(
                            "province",
                            &[Symbol::create_string(&format!("{}", a))?],
                            true,
                        )?,
                        TruthValue::False,
                    )?
                }
                if let Some(a) = instance.loan_interest_type {
                    add_symbol(
                        &mut ctl,
                        Symbol::create_function(
                            "interest",
                            &[Symbol::create_string(&format!("{}", a))?],
                            true,
                        )?,
                        TruthValue::False,
                    )?
                }
                add_symbol(
                    &mut ctl,
                    Symbol::create_id(&format!("{}", instance.loan_type), true)?,
                    TruthValue::False,
                )?;

                match solve_type {
                    SolveType::SAT => ctl_pool_sat.push(ctl),
                    SolveType::MIN_APR => ctl_pool_min_apr.push(ctl),
                    SolveType::MAX_APR => ctl_pool_max_apr.push(ctl),
                    SolveType::MIN_AMOUNT => ctl_pool_min_amount.push(ctl),
                    SolveType::MAX_AMOUNT => ctl_pool_max_amount.push(ctl),
                    SolveType::MIN_TERM => ctl_pool_min_term.push(ctl),
                    SolveType::MAX_TERM => ctl_pool_max_term.push(ctl),
                    SolveType::MIN_FEE => ctl_pool_min_fee.push(ctl),
                    SolveType::MAX_FEE => ctl_pool_max_fee.push(ctl),
                };

                Ok(res)
            };

            loop {
                let (solve_type, instance, query) =
                    worker_in_rec.recv().expect("Valid worker channel died");
                worker_out_send
                    .send(solve_instance(solve_type, instance, query))
                    .expect("Sending work channel results");
            }
        });
        Solver {
            sender: worker_in_send,
            receiver: worker_out_rec,
        }
    }
}

fn get_optimum(handle: &mut SolveHandle) -> Result<Option<Vec<Symbol>>, Error> {
    let mut res: Option<Vec<Symbol>> = None;
    loop {
        handle.resume()?;
        match handle.model() {
            Ok(Some(m)) => {
                res = Some(m.symbols(ShowType::SHOWN)?);
            }
            Ok(_) => break,
            Err(_) => break,
        }
    }
    Ok(res)
}

fn to_term_int(term: BigFrac) -> i32 {
    let term = term / *MS_PER_MONTH;
    let mut scaled = *term.numer().unwrap() as f64
        * (POSSIBLE_VALUES.term_scale as f64 / *term.denom().unwrap() as f64);
    if scaled > std::i32::MAX as f64 {
        scaled = std::i32::MAX as f64;
    }
    let smallest_gte = TERM_TREE
        .range((scaled.ceil() as i32)..)
        .next()
        .cloned()
        .unwrap_or_else(|| TERM_TREE.iter().next_back().unwrap() + 2);
    if (smallest_gte as f64 - scaled).abs() < 0.01 {
        smallest_gte
    } else {
        smallest_gte - 1
    }
}

fn from_apr_int(apr: i32, constraint_type: &ConstraintType) -> ValueConstraint {
    use std::ops::Bound;
    match constraint_type {
        ConstraintType::Max => {
            let smallest_gte = APR_TREE
                .range((Bound::Included(apr), Bound::Unbounded))
                .next()
                .cloned()
                .unwrap_or_else(|| *APR_TREE.iter().next_back().unwrap());
            if smallest_gte == apr {
                ValueConstraint::LTE(BigFrac::new(
                    apr as u64,
                    POSSIBLE_VALUES.apr_scale as u64 * 100,
                ))
            } else {
                ValueConstraint::LT(BigFrac::new(
                    smallest_gte as u64,
                    POSSIBLE_VALUES.apr_scale as u64 * 100,
                ))
            }
        }
        ConstraintType::Min => {
            let largest_lte = APR_TREE
                .range((Bound::Unbounded, Bound::Included(apr)))
                .next_back()
                .cloned()
                .unwrap_or_else(|| *APR_TREE.iter().next().unwrap());
            if largest_lte == apr {
                ValueConstraint::GTE(BigFrac::new(
                    apr as u64,
                    POSSIBLE_VALUES.apr_scale as u64 * 100,
                ))
            } else {
                ValueConstraint::GT(BigFrac::new(
                    largest_lte as u64,
                    POSSIBLE_VALUES.apr_scale as u64 * 100,
                ))
            }
        }
    }
}

fn from_fee_int(fee: i32, constraint_type: &ConstraintType) -> ValueConstraint {
    use std::ops::Bound;
    match constraint_type {
        ConstraintType::Max => {
            let smallest_gte = FEE_TREE
                .range((Bound::Included(fee), Bound::Unbounded))
                .next()
                .cloned()
                .unwrap_or_else(|| *FEE_TREE.iter().next_back().unwrap());
            if smallest_gte == fee {
                ValueConstraint::LTE(BigFrac::new(
                    fee as u64 * 1000,
                    POSSIBLE_VALUES.fee_scale as u64,
                ))
            } else {
                ValueConstraint::LT(BigFrac::new(
                    smallest_gte as u64 * 1000,
                    POSSIBLE_VALUES.fee_scale as u64,
                ))
            }
        }
        ConstraintType::Min => {
            let largest_lte = FEE_TREE
                .range((Bound::Unbounded, Bound::Included(fee)))
                .next_back()
                .cloned()
                .unwrap_or_else(|| *FEE_TREE.iter().next().unwrap());
            if largest_lte == fee {
                ValueConstraint::GTE(BigFrac::new(
                    fee as u64 * 1000,
                    POSSIBLE_VALUES.fee_scale as u64,
                ))
            } else {
                ValueConstraint::GT(BigFrac::new(
                    largest_lte as u64 * 1000,
                    POSSIBLE_VALUES.fee_scale as u64,
                ))
            }
        }
    }
}

fn from_term_int(term: i32, constraint_type: &ConstraintType) -> ValueConstraint {
    use std::ops::Bound;
    match constraint_type {
        ConstraintType::Max => {
            let smallest_gte = TERM_TREE
                .range((Bound::Included(term), Bound::Unbounded))
                .next()
                .cloned()
                .unwrap_or_else(|| *TERM_TREE.iter().next_back().unwrap());
            if smallest_gte == term {
                ValueConstraint::LTE(
                    BigFrac::new(term as u64, POSSIBLE_VALUES.term_scale as u64) * *MS_PER_MONTH,
                )
            } else {
                ValueConstraint::LT(
                    BigFrac::new(smallest_gte as u64, POSSIBLE_VALUES.term_scale as u64)
                        * *MS_PER_MONTH,
                )
            }
        }
        ConstraintType::Min => {
            let largest_lte = TERM_TREE
                .range((Bound::Unbounded, Bound::Included(term)))
                .next_back()
                .cloned()
                .unwrap_or_else(|| *TERM_TREE.iter().next().unwrap());
            if largest_lte == term {
                ValueConstraint::GTE(
                    BigFrac::new(term as u64, POSSIBLE_VALUES.term_scale as u64) * *MS_PER_MONTH,
                )
            } else {
                ValueConstraint::GT(
                    BigFrac::new(largest_lte as u64, POSSIBLE_VALUES.term_scale as u64)
                        * *MS_PER_MONTH,
                )
            }
        }
    }
}

fn produce_base_program(solve_type: SolveType) -> Result<Control, Error> {
    let mut ctl = Control::new(vec![])?;
    ctl.add("base", &[], &*FORMULATION)?;
    ctl.add("base", &[], &*RULES)?;
    match solve_type {
        SolveType::SAT => (),
        SolveType::MIN_APR => ctl.add("base", &[], "#show apr/1.#minimize { A@1: apr(A) }.")?,
        SolveType::MAX_APR => ctl.add("base", &[], "#show apr/1.#maximize { A@1: apr(A) }.")?,
        SolveType::MIN_AMOUNT => {
            ctl.add("base", &[], "#show amount/1.#minimize { A@1: amount(A) }.")?
        }
        SolveType::MAX_AMOUNT => {
            ctl.add("base", &[], "#show amount/1.#maximize { A@1: amount(A) }.")?
        }
        SolveType::MIN_TERM => ctl.add("base", &[], "#show term/1.#minimize { A@1: term(A) }.")?,
        SolveType::MAX_TERM => ctl.add("base", &[], "#show term/1.#maximize { A@1: term(A) }.")?,
        SolveType::MIN_FEE => ctl.add("base", &[], "#show fee/1.#minimize { A@1: fee(A) }.")?,
        SolveType::MAX_FEE => ctl.add("base", &[], "#show fee/1.#maximize { A@1: fee(A) }.")?,
    }
    ctl.ground(&[Part::new("base", &[])?])?;
    ctl.cleanup()?;
    Ok(ctl)
}

fn from_amount_int(amount: i32, constraint_type: &ConstraintType) -> ValueConstraint {
    use std::ops::Bound;
    match constraint_type {
        ConstraintType::Max => {
            let smallest_gte = AMOUNT_TREE
                .range((Bound::Included(amount), Bound::Unbounded))
                .next()
                .cloned()
                .unwrap_or_else(|| *AMOUNT_TREE.iter().next_back().unwrap());
            if smallest_gte == amount {
                ValueConstraint::LTE(BigFrac::new(
                    amount as u64,
                    POSSIBLE_VALUES.amount_scale as u64,
                ))
            } else {
                ValueConstraint::LT(BigFrac::new(
                    smallest_gte as u64,
                    POSSIBLE_VALUES.amount_scale as u64,
                ))
            }
        }
        ConstraintType::Min => {
            let largest_lte = AMOUNT_TREE
                .range((Bound::Unbounded, Bound::Included(amount)))
                .next_back()
                .cloned()
                .unwrap_or_else(|| *AMOUNT_TREE.iter().next().unwrap());
            if largest_lte == amount {
                ValueConstraint::GTE(BigFrac::new(
                    amount as u64,
                    POSSIBLE_VALUES.amount_scale as u64,
                ))
            } else {
                ValueConstraint::GT(BigFrac::new(
                    largest_lte as u64,
                    POSSIBLE_VALUES.amount_scale as u64,
                ))
            }
        }
    }
}
