use lazy_static::lazy_static;
use regex::Regex;
use serde::{de, ser};
use std::fmt;

/// Implements an ISO 3166-2 "Subdivision Code" which is "up to three
/// alphanumeric characters".
/// Source: https://www.iso.org/glossary-for-iso-3166.html
///
/// E.g., the USA uses two letters, Serbia uses 2 digits, and
/// Madagascar uses one letter.
///
/// The end-user BP client code uses the `iso-3166-2` NPM package,
/// https://www.npmjs.com/package/iso-3166-2, and so the intent is for the
/// backend to stay in sync with that spec.
///
/// Applying the New Type Idiom
/// See https://doc.rust-lang.org/stable/rust-by-example/generics/new_types.html
/// Additionally, this creates a private constructor, forcing client code to use
/// the `new` method with validation.
#[derive(<PERSON><PERSON>, Debug, PartialEq, Eq, Hash)]
pub struct SubdivisionCode(String);

/// ISO-3166-2 format, accepting upper or lower case.
const CODE_PATTERN: &'static str = r"^[a-zA-Z0-9]{1,3}$";

impl SubdivisionCode {
    pub fn new(code: impl Into<String>) -> Result<SubdivisionCode, String> {
        lazy_static! {
            static ref CODE_REGEX: Regex = Regex::new(CODE_PATTERN).unwrap();
        }

        let internal_code: String = code.into();
        let temp_slice: &str = internal_code.as_ref();

        match CODE_REGEX.is_match(temp_slice) {
            true => Ok(SubdivisionCode(internal_code)),
            false => Err(format!(
                "Subdivision code must match {}. Received: \"{}\"",
                CODE_PATTERN, temp_slice
            )),
        }
    }
}

impl fmt::Display for SubdivisionCode {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "{}", self.0.to_lowercase())
    }
}

impl<'de> de::Deserialize<'de> for SubdivisionCode {
    fn deserialize<D>(deserializer: D) -> Result<SubdivisionCode, D::Error>
    where
        D: de::Deserializer<'de>,
    {
        let code: String = de::Deserialize::deserialize(deserializer)?;
        Ok(SubdivisionCode::new(code).unwrap())
    }
}

impl ser::Serialize for SubdivisionCode {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: ser::Serializer,
    {
        serializer.serialize_str(self.0.as_ref())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn empty_string_returns_error() {
        let broken_code = SubdivisionCode::new("");
        assert!(broken_code.is_err(), "an empty string is invalid");
    }

    #[test]
    fn three_letters_pass() {
        assert_is_valid("abc");
    }

    #[test]
    fn two_digits_pass() {
        assert_is_valid("45");
    }

    #[test]
    fn one_letter_passes() {
        assert_is_valid("d");
    }

    #[test]
    fn upper_case_letters_pass() {
        // The idea is to be forgiving in what we accept, and strict about
        // what we produce.
        assert_is_valid("CO");
    }

    #[test]
    fn display_returns_a_simple_string() {
        let antsiranana = SubdivisionCode::new("d").unwrap();

        assert_eq!(format!("{}", antsiranana), "d");
    }

    #[test]
    fn display_returns_lower_case() {
        let colorado = SubdivisionCode::new("CO").unwrap();

        assert_eq!(format!("{}", colorado), "co");
    }

    //
    // Helper functions
    //

    fn assert_is_valid(input: &str) {
        match SubdivisionCode::new(input) {
            Ok(_) => assert!(true),
            Err(e) => assert!(false, "{}", e),
        }
    }
}
