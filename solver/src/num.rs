use fraction::Fraction as BigFrac;
use serde::Deserialize;
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, Deserialize, <PERSON><PERSON>Eq, Hash)]
pub struct NUM(#[serde(deserialize_with = "deser_frac")] pub BigFrac);
impl From<BigFrac> for NUM {
    fn from(d: BigFrac) -> Self {
        NUM(d)
    }
}
impl From<NUM> for BigFrac {
    fn from(NUM(d): NUM) -> Self {
        d
    }
}
impl Eq for NUM {}

fn deser_frac<'de, D>(deserializer: D) -> Result<BigFrac, D::Error>
where
    D: serde::de::Deserializer<'de>,
{
    #[derive(Deserialize, Debug)]
    #[serde(untagged)]
    enum StringOrNum {
        String(String),
        Num(f64),
    }

    let value = StringOrNum::deserialize(deserializer)?;

    let value = match value {
        StringOrNum::String(s) => {
            let s = s.trim().to_lowercase();
            let (num_part, multiplier) = if s.ends_with('%') {
                (&s[..s.len() - 1], 0.01)
            } else if s.ends_with('k') {
                (&s[..s.len() - 1], 1_000.0)
            } else if s.ends_with('m') {
                (&s[..s.len() - 1], 1_000_000.0)
            } else {
                (s.as_str(), 1.0)
            };
            let val = num_part
                .parse::<f64>()
                .map_err(serde::de::Error::custom)?;
            val * multiplier
        }
        StringOrNum::Num(n) => n,
    };

    let frac = BigFrac::from(value);
    if frac.is_nan() || frac.is_infinite() {
        return Err(serde::de::Error::custom(
            "value resolved to NaN or Infinity",
        ));
    }
    Ok(frac)
}
