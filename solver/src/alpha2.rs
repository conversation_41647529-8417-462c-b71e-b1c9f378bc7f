use serde::{de, ser};
use std::fmt;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Eq, Hash)]
pub struct Alpha2(pub [char; 2]);
impl fmt::Display for Alpha2 {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        let Alpha2(chars) = self;
        write!(f, "{}{}", chars[0], chars[1])
    }
}
impl<'de> de::Deserialize<'de> for Alpha2 {
    fn deserialize<D>(deserializer: D) -> Result<Alpha2, D::Error>
    where
        D: de::Deserializer<'de>,
    {
        let s: String = de::Deserialize::deserialize(deserializer)?;
        let mut chars = s.chars();
        let char1 = chars.next().ok_or_else(|| {
            de::Error::invalid_length(0, &"Must be a lowercase ISO-Alpha2 designation")
        })?;
        let char2 = chars.next().ok_or_else(|| {
            de::Error::invalid_length(0, &"Must be a lowercase ISO-Alpha2 designation")
        })?;
        Ok(Alpha2([char1, char2]))
    }
}
impl ser::Serialize for Alpha2 {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: ser::Serializer,
    {
        serializer.serialize_str(&format!("{}", self))
    }
}
