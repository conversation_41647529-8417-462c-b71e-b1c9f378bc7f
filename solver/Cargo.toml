[package]
name = "solver"
version = "0.3.0"
authors = ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"]
edition = "2018"

[dependencies]
clingo = "0.6.0"
futures = "0.3.4"
fraction = { version = "0.6.2", features = ["with-serde-support"] }
lazy_static = "1.4.0"
lru-cache = "0.1.2"
num = "0.2.1"
std-semaphore = "0.1.0"
regex = "1"
serde = { version="1.0.104",  features = ["derive"] }
serde_json = "1.0.48"
serde_cbor = "0.11.1"
tokio = {version="0.2.11", features = ["full"]}
pretty_env_logger = "0.4.0"
rweb = "0.4.2"
anyhow = "1.0.26"
log = "0.4.8"

[dev-dependencies]
reqwest = {version="0.10.2", features = ["json"]}
duct = "0.13.3"
locale-codes = "0.3.0"
pretty_assertions = "0.6.1"
