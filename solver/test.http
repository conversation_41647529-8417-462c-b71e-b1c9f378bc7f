### Expecting the legality Meaning $5,000 <= amount <= $25,000,000
POST  http://localhost:3000/v0/legality
Content-Type: application/json

{
  "loan": {
    "country": "us",
    "province": "co",
    "type": "personal"
  },
  "query": "amount"
}

### Expecting not-lendable because it's Madagascar
POST  http://localhost:3000/v0/legality
Content-Type: application/json

{
  "loan": {
    "country": "mg",
    "province": "d",
    "type": "personal",
    "amount": "7000",
    "apr": "0.15"
  }
}

### Expecting the legality API Meaning 0% <= apr < 45%
POST  http://localhost:3000/v0/legality
Content-Type: application/json

{
  "loan": {
    "country": "us",
    "province": "co",
    "type": "personal",
    "amount": "7000"
  },
  "query": "apr"
}

### Expecting the legality Term Meaning 7776000000ms(3 months) <= term <= 93312000000ms (25 Months)
POST  http://localhost:3000/v0/legality
Content-Type: application/json

{
  "loan": {
    "country": "us",
    "province": "co",
    "type": "personal",
    "amount": "7000"
  },
  "query": "term"
}

### Testing loan interest type
POST  http://localhost:3000/v0/legality
Content-Type: application/json

{
  "loan": {
    "country": "us",
    "province": "az",
    "type": "business",
    "loanInterestType": "pni"
  }
}

### Testing loan IO interest type
POST  http://localhost:3000/v0/legality
Content-Type: application/json

{
  "loan": {
    "country": "us",
    "province": "vt",
    "type": "business",
    "loanInterestType": "io"
  }
}


### Expecting the legality Term Meaning the Loan is Legal ([])
POST  http://localhost:3000/v0/legality
Content-Type: application/json

{
  "loan": {
    "country": "us",
    "province": "co",
    "type": "personal",
    "amount": "7000",
    "apr": "0.15"
  }
}

### Expecting the legality Term Meaning the Loan is illegal (null)
POST  http://localhost:3000/v0/legality
Content-Type: application/json

{
  "loan": {
    "country": "us",
    "province": "co",
    "type": "personal",
    "amount": "70000",
    "apr": "0.30"
  },
  "query": null
}


### Dev Server
POST   https://lendable-dsl.dev.saltlending.tech/v0/legality
Content-Type: application/json

{
  "loan": {
    "country": "us",
    "province": "co",
    "type": "personal",
    "amount": "70000",
    "term": "2592000001",
    "apr": "0.30"
  },
  "query": null
}

### Expecting a shallow to be good
GET  http://localhost:3000/status/shallow
Content-Type: application/json

{
  "loan": {
    "country": "us",
    "province": "co",
    "type": "personal"
  },
  "query": "amount"
}