:- apr(A), not aprrange(A).
:- not apr(_).
apr(A) :- apr_ext(A).

:- fee(A), not feerange(A).
:- not fee(_).
fee(A) :- fee_ext(A).

:- amount_to_customer(A), not amount_to_customer_range(A).
amount_to_customer(A) :- amount_to_customer_ext(A).
amountToCustomer(A) :- amount_to_customer(A).

:- illegal, not legal.
:- unfeeable, not feeable.
:- not lendable.
1 { amount(A): amountrange(A) } 1 :- choose_amount.
1 { amt(A): amtrange(A) } 1 :- choose_amt.
1 { apr(A): aprrange(A) } 1 :- choose_apr.
1 { term(A): termrange(A) } 1 :- choose_term.
1 { fee(A): feerange(A) } 1 :- choose_fee.
1 { amount_to_customer(A): amount_to_customer_range(A) } 1 :- choose_amount_to_customer.
#external choose_amount.
#external choose_amt.
#external choose_amount_to_customer.
#external choose_apr.
#external choose_term.
#external choose_fee.
#external personal.
#external business.
#external apr_ext(A) : aprrange(A).
#external fee_ext(A) : feerange(A).
#external amount_to_customer_ext(A) : amount_to_customer_range(A).