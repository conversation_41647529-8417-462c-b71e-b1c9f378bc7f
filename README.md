# Lendable DSL
```
AMOUNT RANGE 5k-25m.
TERM RANGE 1mo-3yr.

ILLEGAL IF apr <= 3.14%.
ILLEGAL IF apr <= 4% AND amount != 10m.

FOR COUNTRY us AND PERSONAL:
  LENDABLE.
  ILLEGAL IF term >= 1yr.
  FOR PROVINCE co:
    ILLEGAL IF amount > 75k.
    ILLEGAL IF apr > 45%.
    LEGAL IF amount = 10m.
  ILLEGAL IF PROVINCE az OR PROVINCE tx.
  LEGAL IF PROVINCE az AND apr < 6%.
```

All documentation will refer to the above example.



.lnd files must begin with an `AMOUNT RANGE` statement.  The statment on line 1 begins with the words `AMOUNT RANGE` and is followed by two `amount`s separated by a `-`.
An `amount` is a number followed by either `k` or `m`.
`k` represents thousands, `m` represents millions

The second statement must be a `TERM RANGE` statement.  The statment on line 2 begins with the words `TERM RANGE` and is followed by two `time`s separated by a `-`.
A `time` is a number followed by either `mo` or `yr`.
`mo` represents months, `yr` represents years

On line 4 we encounter our first `ILLEGAL IF` statement.  An `ILLEGAL IF` statement is comprised of the words `ILLEGAL IF` followed by an expression, followed by a `.` at the end of the line.  In this case the expression is `apr <= 3.14%`.
The line as a whole says that any loan is illegal if the `apr` is less than or equal to `3.14%`.

Line 5 is very similar to line 4.  The difference is that the expression in line 4's `ILLEGAL IF` statement has an `AND` operator.
Line 5 says that if the `apr` is less than or equal to `4%` `AND` the `amount` of a loan is not equal to `10m`, the loan is illegal.

Lines 4 and 5 can be combined into the following.

`ILLEGAL IF apr <= 3.14% OR (apr <= 4% AND amount != 10m).`

Lines 4 and 5 apply to every loan.


`FOR` statements are a tool to limit the scope of your `ILLEGAL` or `LEGAL` statments.

`FOR` statements are comprised of the word `FOR`, an expression, and end with `:`.

Indents of 2 spaces or "tabs" determine whether a legal statement applies to the scope of the for expression.

Lines 11-13 apply to the scopes of both the `FOR` statement on line 10 and 7, while line 14-15 apply only to the `FOR` statement on line 7.

Line 11 translates to.

  A loan is `ILLEGAL IF` it is in the `COUNTRY us`, it is `PERSONAL`, the `PROVINCE co`, and the `amount` is greater than `75k`.

The `LEGAL IF` statement on line 11 overrides the `ILLEGAL IF` statement on lines 11 and 12.
Meaning, a loan is `ILLEGAL IF` it is greater than `amount` `75k`, or it is greater that `45%` `apr`, UNLESS that loan's `amount` is equal to `10m`.

The word `LENDABLE` on line 8 states that all loans in us (within the scope of the `FOR`) are `LENDABLE`.
`ILLEGAL IF` statements override the lendability of a scope.
`LEGAL IF` statements override the non lendability of illegal statements.

## Variables: Type
  - `amount`: Amount
  - `term`: Time
  - `apr`: Pecentage

## Amount Units: 
  - `k`
  - `m`

## Time Units:
  - `mo`
  - `yr`

## Percentage Units:
  - `%`

## Boolean Operators:
  - `AND`
  - `OR`
  - `NOT`

## Number Comparison Operators:
  - `<=` - less than or equal to
  - `>=` - greater than or equal to
  - `<` - less than
  - `>` - greater than
  - `=` - equal
  - `!=` - not equal

## Other statements
  - `PERSONAL` - If a loan is for personal use.
  - `BUSINESS` - If a loan is for business use.
  - `COUNTRY us` - If a loan is in the country of the US. (use the ISO-alpha2 designation for all countries)
  - `PROVINCE co` - If a loan is in the province of Colorado. (use the ISO-alpha2 designation for provinces)
  - `TRUE` - True for all loans. This term has very limited use, but exists just in case it is necessary.
  - `FALSE` - False for all loans. This term has very limited use, but exists just in case it is necessary.
  - `INTEREST io` - If a loan has conditions for various loan interest types. Typically applies to denote an illegal case when a jurisdiction has an IO restriction on personal or business loans.

## General 
- If we do not have a license in the jurisdiction, safe harbor restrictions apply
- If we have a license in the jurisdiction, this constraint typically overwrites the usary rate restrictions
- Usuary rate restrictions override safe harbor when we have no license only if they are more restrictive
- The license allows us to lend in these limits i.e. the restrictions apply without a license
- Safe harbor restrictions should be AND i.e. to meet the safe harbor, the loan must be larger than 15k AND have an interest rate less than 12%
- If spreadsheet says "All" in safe harbor section, all rates/amounts are allowed (no restrictions)
- If spreadsheet says "All" in license required section, all rates/amounts are restricted unless we have acquired a license
- rule of thumb: if there is a license, ignore the usury rates, unless noted (spreadsheet notes column)
- If the IO restriction is consumer only, and the restriction type is must be allowed to refi, this means:
    - business loans can be IO
    - consumer loans can be IO, only if they are allowed to refi
- There are some exceptions to above rules that legal will denote

# Runbook
- run `sh build.sh` to fully complile artifacts
- to test: `cd solver && cargo test`

# Change Process
- Open `authoritative-rules.lnd` file in browser
- Select `Edit this file` icon on right hand side
- Make appropriate changes
- Enter a commit title and message
- Select create a new branch to start a pull request
- Commit changes and complete flow to set up pull request in the next screen
- Copy pull request URL and paste in #engineering Slack channel for developers to review
- Once reviewed, the change will be merged into the development environment and follow our sprint cadence for release to production
